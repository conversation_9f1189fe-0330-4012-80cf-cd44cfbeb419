# Manuálne príkazy pre generovanie ob<PERSON><PERSON><PERSON><PERSON> - Scenario API

## 1. Nastavenie API kľúča
```bash
export SCENARIO_API_KEY="api_BcbAFAXVD1HcedCEKw7hmhLW"
```

## 2. Prechod do priečinka
```bash
cd ~/Desktop/hlavolamy/1_sifra
```

## 3. Generovanie jednotliv<PERSON>ch obr<PERSON><PERSON>kov pomocou curl

### bg_cipher_parchment_1080.png (1080×1920)
```bash
curl -X POST "https://api.cloud.scenario.com/v1/generate/txt2img" \
  -H "Authorization: Bearer $SCENARIO_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "ancient parchment texture, dark gothic stains, seamless, no text, style:\"Prekliate_dedicstvo\", no background",
    "width": 1080,
    "height": 1920,
    "numSamples": 1,
    "guidance": 7.5,
    "scheduler": "EulerDiscreteScheduler"
  }'
```

### frame_input_600x90.png (600×90)
```bash
curl -X POST "https://api.cloud.scenario.com/v1/generate/txt2img" \
  -H "Authorization: Bearer $SCENARIO_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "ornate brass frame, horizontal rectangle, fits text input, dark gothic engraving, no background, style:\"Prekliate_dedicstvo\"",
    "width": 600,
    "height": 90,
    "numSamples": 1,
    "guidance": 7.5,
    "scheduler": "EulerDiscreteScheduler"
  }'
```

### btn_submit_300x90.png (300×90)
```bash
curl -X POST "https://api.cloud.scenario.com/v1/generate/txt2img" \
  -H "Authorization: Bearer $SCENARIO_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "rectangular button, embossed gothic pattern, dark gold, high relief, no text, no background, style:\"Prekliate_dedicstvo\"",
    "width": 300,
    "height": 90,
    "numSamples": 1,
    "guidance": 7.5,
    "scheduler": "EulerDiscreteScheduler"
  }'
```

### icon_error_64.png (64×64)
```bash
curl -X POST "https://api.cloud.scenario.com/v1/generate/txt2img" \
  -H "Authorization: Bearer $SCENARIO_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "cracked blood-red wax seal, sharp edges, no background, style:\"Prekliate_dedicstvo\"",
    "width": 64,
    "height": 64,
    "numSamples": 1,
    "guidance": 7.5,
    "scheduler": "EulerDiscreteScheduler"
  }'
```

### title_cipher_800.png (800×200)
```bash
curl -X POST "https://api.cloud.scenario.com/v1/generate/txt2img" \
  -H "Authorization: Bearer $SCENARIO_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "calligraphic banner, text:\"Dekóduj správu\", black ink on torn parchment strip, no background, style:\"Prekliate_dedicstvo\"",
    "width": 800,
    "height": 200,
    "numSamples": 1,
    "guidance": 7.5,
    "scheduler": "EulerDiscreteScheduler"
  }'
```

## 4. Vyčistenie API kľúča
```bash
unset SCENARIO_API_KEY
```

## 5. Poznámky
- Obrázky sa generujú asynchrónne
- Skontroluj svoj Scenario účet pre stiahnutie hotových obrázkov
- API vráti inference ID, ktoré môžeš použiť na sledovanie stavu
