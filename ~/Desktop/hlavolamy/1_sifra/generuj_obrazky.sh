#!/bin/bash

# Skript na generovanie o<PERSON><PERSON><PERSON><PERSON><PERSON> pre Puzzle 1 - Šifra
# API: api_BcbAFAXVD1HcedCEKw7hmhLW

API_KEY="api_BcbAFAXVD1HcedCEKw7hmhLW"
OUTPUT_DIR="~/Desktop/hlavolamy/1_sifra"

echo "Generujem obrázky pre Puzzle 1 - Šifra..."

# 1) Pozadie pergamenu
echo "1/5 - Generujem bg_cipher_parchment_1080.png..."
# curl -X POST "YOUR_API_ENDPOINT" \
#   -H "Authorization: Bearer $API_KEY" \
#   -H "Content-Type: application/json" \
#   -d '{
#     "prompt": "ancient parchment texture, dark gothic stains, seamless, no text, style:\"Prekliate_dedicstvo\", no background",
#     "size": "1080x1920",
#     "output": "'$OUTPUT_DIR'/bg_cipher_parchment_1080.png"
#   }'

# 2) <PERSON><PERSON><PERSON> pre input
echo "2/5 - Generujem frame_input_600x90.png..."
# curl -X POST "YOUR_API_ENDPOINT" \
#   -H "Authorization: Bearer $API_KEY" \
#   -H "Content-Type: application/json" \
#   -d '{
#     "prompt": "ornate brass frame, horizontal rectangle, fits text input, dark gothic engraving, no background, style:\"Prekliate_dedicstvo\"",
#     "size": "600x90",
#     "output": "'$OUTPUT_DIR'/frame_input_600x90.png"
#   }'

# 3) Tlačidlo submit
echo "3/5 - Generujem btn_submit_300x90.png..."
# curl -X POST "YOUR_API_ENDPOINT" \
#   -H "Authorization: Bearer $API_KEY" \
#   -H "Content-Type: application/json" \
#   -d '{
#     "prompt": "rectangular button, embossed gothic pattern, dark gold, high relief, no text, no background, style:\"Prekliate_dedicstvo\"",
#     "size": "300x90",
#     "output": "'$OUTPUT_DIR'/btn_submit_300x90.png"
#   }'

# 4) Ikona chyby
echo "4/5 - Generujem icon_error_64.png..."
# curl -X POST "YOUR_API_ENDPOINT" \
#   -H "Authorization: Bearer $API_KEY" \
#   -H "Content-Type: application/json" \
#   -d '{
#     "prompt": "cracked blood-red wax seal, sharp edges, no background, style:\"Prekliate_dedicstvo\"",
#     "size": "64x64",
#     "output": "'$OUTPUT_DIR'/icon_error_64.png"
#   }'

# 5) Titulok
echo "5/5 - Generujem title_cipher_800.png..."
# curl -X POST "YOUR_API_ENDPOINT" \
#   -H "Authorization: Bearer $API_KEY" \
#   -H "Content-Type: application/json" \
#   -d '{
#     "prompt": "calligraphic banner, text:\"Dekóduj správu\", black ink on torn parchment strip, no background, style:\"Prekliate_dedicstvo\"",
#     "size": "800x200",
#     "output": "'$OUTPUT_DIR'/title_cipher_800.png"
#   }'

echo "Hotovo! Všetky obrázky by mali byť vygenerované v priečinku: $OUTPUT_DIR"

# Poznámka: Tento skript je šablóna. Musíš:
# 1. Nahradiť "YOUR_API_ENDPOINT" skutočným API endpointom
# 2. Upraviť formát požiadavky podľa dokumentácie tvojho API
# 3. Spustiť skript: chmod +x generuj_obrazky.sh && ./generuj_obrazky.sh
