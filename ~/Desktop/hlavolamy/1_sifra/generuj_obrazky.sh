#!/bin/bash

# Skript na generovanie o<PERSON><PERSON><PERSON><PERSON><PERSON> pre Puzzle 1 - Šifra pomocou Scenario CLI
# API: api_BcbAFAXVD1HcedCEKw7hmhLW

# Nastavenie API kľúča ako premenná prostredia
export SCENARIO_API_KEY="api_BcbAFAXVD1HcedCEKw7hmhLW"

# Cesta k výstupnému priečinku
OUTPUT_DIR="$HOME/Desktop/hlavolamy/1_sifra"
MODEL="Prekliate_dedicstvo"

echo "🎨 Generujem obrázky pre Puzzle 1 - Šifra pomocou Scenario CLI..."
echo "📁 Výstupný priečinok: $OUTPUT_DIR"
echo "🤖 Model: $MODEL"
echo ""

# Kont<PERSON>a, či je Scenario CLI nainštalované
if ! command -v scenario &> /dev/null; then
    echo "❌ Scenario CLI nie je nainštalované!"
    echo "💡 Nainštaluj ho pomocí: npm install -g @scenario-gg/cli"
    exit 1
fi

# Prechod do výstupného priečinka
cd "$OUTPUT_DIR" || exit 1

# 1) Pozadie pergamenu
echo "1/5 - 📜 Generujem bg_cipher_parchment_1080.png..."
scenario prompt run "ancient parchment texture, dark gothic stains, seamless, no text, style:\"Prekliate_dedicstvo\", no background" \
    --model "$MODEL" \
    --width 1080 \
    --height 1920 \
    --output "bg_cipher_parchment_1080.png"

if [ $? -eq 0 ]; then
    echo "✅ bg_cipher_parchment_1080.png - HOTOVO"
else
    echo "❌ bg_cipher_parchment_1080.png - CHYBA"
fi
echo ""

# 2) Rám pre input
echo "2/5 - 🖼️ Generujem frame_input_600x90.png..."
scenario prompt run "ornate brass frame, horizontal rectangle, fits text input, dark gothic engraving, no background, style:\"Prekliate_dedicstvo\"" \
    --model "$MODEL" \
    --width 600 \
    --height 90 \
    --output "frame_input_600x90.png"

if [ $? -eq 0 ]; then
    echo "✅ frame_input_600x90.png - HOTOVO"
else
    echo "❌ frame_input_600x90.png - CHYBA"
fi
echo ""

# 3) Tlačidlo submit
echo "3/5 - 🔘 Generujem btn_submit_300x90.png..."
scenario prompt run "rectangular button, embossed gothic pattern, dark gold, high relief, no text, no background, style:\"Prekliate_dedicstvo\"" \
    --model "$MODEL" \
    --width 300 \
    --height 90 \
    --output "btn_submit_300x90.png"

if [ $? -eq 0 ]; then
    echo "✅ btn_submit_300x90.png - HOTOVO"
else
    echo "❌ btn_submit_300x90.png - CHYBA"
fi
echo ""

# 4) Ikona chyby
echo "4/5 - ⚠️ Generujem icon_error_64.png..."
scenario prompt run "cracked blood-red wax seal, sharp edges, no background, style:\"Prekliate_dedicstvo\"" \
    --model "$MODEL" \
    --width 64 \
    --height 64 \
    --output "icon_error_64.png"

if [ $? -eq 0 ]; then
    echo "✅ icon_error_64.png - HOTOVO"
else
    echo "❌ icon_error_64.png - CHYBA"
fi
echo ""

# 5) Titulok
echo "5/5 - 📋 Generujem title_cipher_800.png..."
scenario prompt run "calligraphic banner, text:\"Dekóduj správu\", black ink on torn parchment strip, no background, style:\"Prekliate_dedicstvo\"" \
    --model "$MODEL" \
    --width 800 \
    --height 200 \
    --output "title_cipher_800.png"

if [ $? -eq 0 ]; then
    echo "✅ title_cipher_800.png - HOTOVO"
else
    echo "❌ title_cipher_800.png - CHYBA"
fi
echo ""

# Vyčistenie API kľúča z pamäte
unset SCENARIO_API_KEY

echo "🎉 Generovanie dokončené!"
echo "📁 Všetky obrázky sú uložené v: $OUTPUT_DIR"
echo "🔍 Zoznam vygenerovaných súborov:"
ls -la *.png 2>/dev/null || echo "⚠️ Žiadne PNG súbory neboli nájdené"

echo ""
echo "🔒 API kľúč bol odstránený z pamäte shellu."
