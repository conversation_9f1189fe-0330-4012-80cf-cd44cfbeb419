#!/bin/bash

# Skript na generovanie ob<PERSON><PERSON><PERSON><PERSON> pre Puzzle 1 - Šifra pomocou Scenario API
# API: api_BcbAFAXVD1HcedCEKw7hmhLW

# Nastavenie API kľúča ako premenná prostredia
export SCENARIO_API_KEY="api_BcbAFAXVD1HcedCEKw7hmhLW"

# API endpoint a cesta k výstupnému priečinku
API_ENDPOINT="https://api.cloud.scenario.com/v1/generate/txt2img"
OUTPUT_DIR="$HOME/Desktop/hlavolamy/1_sifra"

echo "🎨 Generujem obrázky pre Puzzle 1 - Šifra pomocou Scenario API..."
echo "📁 Výstupný priečinok: $OUTPUT_DIR"
echo "🌐 API Endpoint: $API_ENDPOINT"
echo ""

# Kontrola, či je curl nainštalované
if ! command -v curl &> /dev/null; then
    echo "❌ curl nie je nainštalované!"
    exit 1
fi

# Prechod do výstupného priečinka
cd "$OUTPUT_DIR" || exit 1

# Funkcia na generovanie obrázka
generate_image() {
    local prompt="$1"
    local width="$2"
    local height="$3"
    local filename="$4"
    local description="$5"
    
    echo "🎨 $description..."
    echo "📝 Prompt: $prompt"
    echo "📐 Rozmer: ${width}x${height}"
    
    # API volanie
    response=$(curl -s -X POST "$API_ENDPOINT" \
        -H "Authorization: Bearer $SCENARIO_API_KEY" \
        -H "Content-Type: application/json" \
        -d "{
            \"prompt\": \"$prompt\",
            \"width\": $width,
            \"height\": $height,
            \"numSamples\": 1,
            \"guidance\": 7.5,
            \"scheduler\": \"EulerDiscreteScheduler\"
        }")
    
    # Kontrola odpovede
    if echo "$response" | grep -q "inference"; then
        echo "✅ $filename - API volanie úspešné"
        echo "📄 Odpoveď: $response"
    else
        echo "❌ $filename - API volanie neúspešné"
        echo "📄 Chyba: $response"
    fi
    echo ""
}

# 1) Pozadie pergamenu
generate_image \
    "ancient parchment texture, dark gothic stains, seamless, no text, style:\"Prekliate_dedicstvo\", no background" \
    1080 \
    1920 \
    "bg_cipher_parchment_1080.png" \
    "1/5 - 📜 Generujem bg_cipher_parchment_1080.png"

# 2) Rám pre input
generate_image \
    "ornate brass frame, horizontal rectangle, fits text input, dark gothic engraving, no background, style:\"Prekliate_dedicstvo\"" \
    600 \
    90 \
    "frame_input_600x90.png" \
    "2/5 - 🖼️ Generujem frame_input_600x90.png"

# 3) Tlačidlo submit
generate_image \
    "rectangular button, embossed gothic pattern, dark gold, high relief, no text, no background, style:\"Prekliate_dedicstvo\"" \
    300 \
    90 \
    "btn_submit_300x90.png" \
    "3/5 - 🔘 Generujem btn_submit_300x90.png"

# 4) Ikona chyby
generate_image \
    "cracked blood-red wax seal, sharp edges, no background, style:\"Prekliate_dedicstvo\"" \
    64 \
    64 \
    "icon_error_64.png" \
    "4/5 - ⚠️ Generujem icon_error_64.png"

# 5) Titulok
generate_image \
    "calligraphic banner, text:\"Dekóduj správu\", black ink on torn parchment strip, no background, style:\"Prekliate_dedicstvo\"" \
    800 \
    200 \
    "title_cipher_800.png" \
    "5/5 - 📋 Generujem title_cipher_800.png"

# Vyčistenie API kľúča z pamäte
unset SCENARIO_API_KEY

echo "🎉 Generovanie dokončené!"
echo "📁 Všetky API volania boli odoslané"
echo "💡 Poznámka: Obrázky sa generujú asynchrónne. Skontroluj svoj Scenario účet pre stiahnutie."
echo ""
echo "🔒 API kľúč bol odstránený z pamäte shellu."
